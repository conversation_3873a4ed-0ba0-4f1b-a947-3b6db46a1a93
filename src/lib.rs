extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{<PERSON>sonRead<PERSON>, JsonStreamReader};



/// Represents a JSON value type with proper recursive semantics
#[derive(Debug, Clone, PartialEq)]
enum JsonValueType {
    Null,
    Boolean,
    Number,
    String,
    Array(Box<JsonValueType>),      // Array of elements of this type
    Object(Vec<JsonField>),         // Object with named fields
}

/// Represents a field in a JSON object
#[derive(Debug, Clone, PartialEq)]
struct JsonField {
    name: String,
    value_type: JsonValueType,
}



/// Represents the discovered JSON schema with recursive structure
#[derive(Debug, Clone)]
struct JsonSchema {
    root_type: JsonValueType,
    columns: Vec<StructuredColumn>,
}

/// Represents a column with proper structured types (STRUCT/ARRAY)
#[derive(Debug, Clone)]
struct StructuredColumn {
    name: String,
    value_type: JsonValueType,
}



#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    schema: JsonSchema,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    projected_columns: Vec<usize>, // Columns requested by the query
}

struct JsonReaderVTab;

// Helper function to discover JSON schema with proper recursive analysis
fn discover_json_schema(
    file_path: &str,
    projected_columns: Option<&[usize]>
) -> Result<JsonSchema, Box<dyn std::error::Error>> {
    eprintln!("!!! DISCOVER_JSON_SCHEMA CALLED WITH FILE: {}", file_path);
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Analyze the root JSON structure recursively
    let root_type = analyze_json_value(&mut json_reader)?;

    // Generate structured columns based on the discovered structure
    let columns = if let Some(projected) = projected_columns {
        // Query-driven: only generate columns for projected fields
        eprintln!("DEBUG SCHEMA: Using projected columns mode");
        generate_projected_columns(&root_type, projected)?
    } else {
        // Discovery mode: generate all possible columns
        eprintln!("DEBUG SCHEMA: Using discovery mode, calling generate_all_columns");
        eprintln!("DEBUG SCHEMA: Root type: {:?}", root_type);
        let cols = generate_all_columns(&root_type)?;
        eprintln!("DEBUG SCHEMA: Generated columns: {:?}", cols);
        cols
    };

    if columns.is_empty() {
        // Handle empty objects by creating a single column like DuckDB's built-in JSON reader
        // DuckDB requires table functions to return at least one column
        return Ok(JsonSchema {
            root_type,
            columns: vec![StructuredColumn {
                name: "json".to_string(),
                value_type: JsonValueType::String, // Will contain the JSON string representation
            }],
        });
    }

    Ok(JsonSchema {
        root_type,
        columns,
    })
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data
//
// Recursively analyze JSON structure to build proper type representation
fn analyze_json_value(
    json_reader: &mut JsonStreamReader<BufReader<File>>
) -> Result<JsonValueType, Box<dyn std::error::Error>> {
    match json_reader.peek()? {
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(JsonValueType::Null)
        }
        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(JsonValueType::Boolean)
        }
        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(JsonValueType::Number)
        }
        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(JsonValueType::String)
        }
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;

            // Analyze first element to determine array element type
            let element_type = if json_reader.has_next()? {
                let first_element_type = analyze_json_value(json_reader)?;

                // Skip remaining elements for now (we could analyze more for union types)
                while json_reader.has_next()? {
                    json_reader.skip_value()?;
                }

                first_element_type
            } else {
                // Empty array - assume string elements
                JsonValueType::String
            };

            json_reader.end_array()?;
            Ok(JsonValueType::Array(Box::new(element_type)))
        }
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut fields = Vec::new();

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_type = analyze_json_value(json_reader)?;

                fields.push(JsonField {
                    name: field_name,
                    value_type: field_type,
                });
            }

            json_reader.end_object()?;
            Ok(JsonValueType::Object(fields))
        }
    }
}

// Generate columns for projected fields only
fn generate_projected_columns(
    _root_type: &JsonValueType,
    _projected: &[usize]
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    // TODO: Implement query-driven column generation
    Err("Query-driven column generation not yet implemented".into())
}

// Generate structured columns from JSON structure (preserving hierarchy)
fn generate_all_columns(
    root_type: &JsonValueType
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    match root_type {
        JsonValueType::Object(fields) => {
            // Root object: each field becomes a top-level column
            let mut columns = Vec::new();
            for field in fields {
                columns.push(StructuredColumn {
                    name: field.name.clone(),
                    value_type: field.value_type.clone(),
                });
            }
            Ok(columns)
        }
        JsonValueType::Array(element_type) => {
            // Root array: flatten array elements into rows
            eprintln!("DEBUG SCHEMA: Root array detected, flattening elements into rows");
            match element_type.as_ref() {
                JsonValueType::Object(fields) => {
                    // Array of objects: each object field becomes a column
                    eprintln!("DEBUG SCHEMA: Array of objects with {} fields", fields.len());
                    let mut columns = Vec::new();
                    for field in fields {
                        eprintln!("DEBUG SCHEMA: Adding column '{}' with type {:?}", field.name, field.value_type);
                        columns.push(StructuredColumn {
                            name: field.name.clone(),
                            value_type: field.value_type.clone(),
                        });
                    }
                    eprintln!("DEBUG SCHEMA: Generated {} flattened columns", columns.len());
                    Ok(columns)
                }
                _ => {
                    // Array of primitives: single column with element type
                    eprintln!("DEBUG SCHEMA: Array of primitives, creating single 'value' column");
                    Ok(vec![StructuredColumn {
                        name: "value".to_string(),
                        value_type: element_type.as_ref().clone(),
                    }])
                }
            }
        }
        _ => {
            // Root primitive: single column
            Ok(vec![StructuredColumn {
                name: "value".to_string(),
                value_type: root_type.clone(),
            }])
        }
    }
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data
//
// Convert JsonValueType to DuckDB LogicalTypeHandle
fn json_type_to_duckdb_type(json_type: &JsonValueType) -> Result<LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        JsonValueType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        JsonValueType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)), // Use proper Double type for numbers
        JsonValueType::String => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Array(element_type) => {
            // Create proper LIST type with correct element type
            let element_logical_type = json_type_to_duckdb_type(element_type)?;
            Ok(LogicalTypeHandle::list(&element_logical_type))
        }
        JsonValueType::Object(fields) => {
            // Create proper STRUCT type for nested objects
            let mut struct_fields = Vec::new();

            for field in fields {
                let field_type = json_type_to_duckdb_type(&field.value_type)?;
                struct_fields.push((field.name.as_str(), field_type));
            }

            Ok(LogicalTypeHandle::struct_type(&struct_fields))
        }
    }
}

// Apply projection pushdown to only include requested columns
fn apply_projection_pushdown(
    full_schema: &JsonSchema,
    projected_column_names: &[String],
) -> JsonSchema {
    eprintln!("DEBUG PROJECTION: Applying pushdown for columns: {:?}", projected_column_names);

    // If no specific projection, return full schema
    if projected_column_names.is_empty() {
        eprintln!("DEBUG PROJECTION: No projection specified, using full schema");
        return full_schema.clone();
    }

    // Filter columns to only include projected ones
    let mut projected_columns = Vec::new();

    for column_name in projected_column_names {
        if let Some(column) = full_schema.columns.iter().find(|col| &col.name == column_name) {
            eprintln!("DEBUG PROJECTION: Including column: {}", column_name);
            projected_columns.push(column.clone());
        } else {
            eprintln!("DEBUG PROJECTION: Warning - requested column '{}' not found in schema", column_name);
        }
    }

    JsonSchema {
        root_type: full_schema.root_type.clone(),
        columns: projected_columns,
    }
}

// Read JSON file using streaming parser and write directly to DuckDB vectors for memory efficiency
fn read_json_streaming_to_vectors(
    file_path: &str,
    schema: &JsonSchema,
    output: &DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    use std::fs::File;
    use struson::reader::{JsonReader, JsonStreamReader};

    eprintln!("DEBUG STREAMING: Opening file for streaming JSON parsing: {}", file_path);

    // Validate file exists and is readable
    if !std::path::Path::new(file_path).exists() {
        return Err(format!("JSON file not found: {}", file_path).into());
    }

    // Open file for streaming with error handling
    let file = match File::open(file_path) {
        Ok(f) => f,
        Err(e) => return Err(format!("Failed to open JSON file '{}': {}", file_path, e).into()),
    };

    let mut json_reader = JsonStreamReader::new(file);
    let mut row_count = 0;

    // Start reading the JSON structure with comprehensive error handling
    match json_reader.peek() {
        Ok(value_type) => match value_type {
            struson::reader::ValueType::Object => {
                eprintln!("DEBUG STREAMING: Processing single JSON object");
                match read_object_streaming_to_vectors(&mut json_reader, schema, output, row_count) {
                    Ok(()) => row_count += 1,
                    Err(e) => return Err(format!("Error parsing JSON object: {}", e).into()),
                }
            }
            struson::reader::ValueType::Array => {
                eprintln!("DEBUG STREAMING: Processing JSON array");
                match json_reader.begin_array() {
                    Ok(_) => {
                        while json_reader.has_next().unwrap_or(false) {
                            if row_count > 10000 {
                                eprintln!("DEBUG STREAMING: Warning - processing large array with {} elements", row_count + 1);
                            }

                            // For root-level arrays, each element becomes a row with flattened columns
                            match read_array_element_as_row(&mut json_reader, schema, output, row_count) {
                                Ok(()) => row_count += 1,
                                Err(e) => {
                                    eprintln!("DEBUG STREAMING: Warning - skipping malformed array element at position {}: {}", row_count + 1, e);
                                    // Try to skip the malformed element
                                    if let Err(skip_err) = json_reader.skip_value() {
                                        return Err(format!("Failed to skip malformed element: {}", skip_err).into());
                                    }
                                }
                            }
                        }

                        if let Err(e) = json_reader.end_array() {
                            return Err(format!("Error closing JSON array: {}", e).into());
                        }
                    }
                    Err(e) => return Err(format!("Error starting JSON array: {}", e).into()),
                }
            }
            _ => {
                return Err(format!("Unsupported JSON root type: expected object or array, found {:?}", value_type).into());
            }
        },
        Err(e) => {
            return Err(format!("Failed to read JSON file '{}': {} (file may be empty or malformed)", file_path, e).into());
        }
    }

    eprintln!("DEBUG STREAMING: Successfully parsed {} rows using streaming", row_count);

    if row_count == 0 {
        eprintln!("DEBUG STREAMING: Warning - no valid data rows found in JSON file");
    }

    Ok(row_count)
}

// Read a single array element and write it as a flattened row
fn read_array_element_as_row(
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
    output: &DataChunkHandle,
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Object => {
            // Object element: read fields and map to flattened columns
            json_reader.begin_object()?;

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                eprintln!("DEBUG ARRAY_FLATTEN: Processing field: {}", field_name);

                // Find the column index for this field in the flattened schema
                if let Some(col_idx) = schema.columns.iter().position(|col| col.name == field_name) {
                    eprintln!("DEBUG ARRAY_FLATTEN: Found column {} for field {}", col_idx, field_name);

                    // Read the field value directly into the appropriate column
                    match read_value_streaming_to_vector(json_reader, output, col_idx, row_idx, &schema.columns[col_idx].value_type) {
                        Ok(()) => {
                            eprintln!("DEBUG ARRAY_FLATTEN: Successfully inserted field '{}' at column {}", field_name, col_idx);
                        }
                        Err(e) => {
                            eprintln!("DEBUG ARRAY_FLATTEN: Error inserting field '{}': {}", field_name, e);
                            // Set null value for this field
                            set_vector_null(output, col_idx, row_idx, &schema.columns[col_idx].value_type);
                            // Try to skip the problematic value
                            if let Err(skip_err) = json_reader.skip_value() {
                                return Err(format!("Failed to skip malformed field '{}': {}", field_name, skip_err).into());
                            }
                        }
                    }
                } else {
                    // Field not in schema - skip it
                    eprintln!("DEBUG ARRAY_FLATTEN: Skipping unknown field: {}", field_name);
                    json_reader.skip_value()?;
                }
            }

            json_reader.end_object()?;
            Ok(())
        }
        _ => {
            // Primitive element: write to single column (assuming schema has one column)
            if schema.columns.len() == 1 {
                read_value_streaming_to_vector(json_reader, output, 0, row_idx, &schema.columns[0].value_type)
            } else {
                Err("Primitive array element but schema has multiple columns".into())
            }
        }
    }
}

// Read a JSON object using streaming parser and write directly to DuckDB vectors
fn read_object_streaming_to_vectors(
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
    output: &DataChunkHandle,
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG STREAMING: Reading object to vectors at row {}", row_idx);

    // Begin object with error handling
    if let Err(e) = json_reader.begin_object() {
        return Err(format!("Failed to start reading JSON object: {}", e).into());
    }

    // Check if this is an empty object case (single "json" column for empty objects)
    let is_empty_object_case = schema.columns.len() == 1 &&
                               schema.columns[0].name == "json" &&
                               matches!(schema.columns[0].value_type, JsonValueType::String);

    let mut field_count = 0;
    while json_reader.has_next().unwrap_or(false) {
        field_count += 1;

        // Prevent infinite loops on malformed JSON
        if field_count > 1000 {
            return Err("JSON object has too many fields (>1000), possible malformed JSON".into());
        }

        let field_name = match json_reader.next_name() {
            Ok(name) => name.to_string(),
            Err(e) => return Err(format!("Failed to read field name at position {}: {}", field_count, e).into()),
        };

        // Find the column index for this field
        if let Some(col_idx) = schema.columns.iter().position(|col| col.name == field_name) {
            // Only parse fields that are in our schema (projection pushdown)
            eprintln!("DEBUG PROJECTION: Parsing required field: {}", field_name);
            match read_value_streaming_to_vector(json_reader, output, col_idx, row_idx, &schema.columns[col_idx].value_type) {
                Ok(()) => {
                    eprintln!("DEBUG STREAMING: Successfully parsed field '{}' at column {}", field_name, col_idx);
                }
                Err(e) => {
                    eprintln!("DEBUG ERROR: Failed to parse field '{}': {}", field_name, e);
                    // Set null value for this field
                    set_vector_null(output, col_idx, row_idx, &schema.columns[col_idx].value_type);
                    // Try to skip the problematic value
                    if let Err(skip_err) = json_reader.skip_value() {
                        return Err(format!("Failed to skip malformed field '{}': {}", field_name, skip_err).into());
                    }
                }
            }
        } else {
            // Skip fields not in schema (projection optimization)
            eprintln!("DEBUG PROJECTION: Skipping unrequested field: {}", field_name);
            if let Err(e) = json_reader.skip_value() {
                return Err(format!("Failed to skip unrequested field '{}': {}", field_name, e).into());
            }
        }
    }

    // End object with error handling
    if let Err(e) = json_reader.end_object() {
        return Err(format!("Failed to close JSON object: {}", e).into());
    }

    // Handle empty object case - set JSON string representation
    if is_empty_object_case && field_count == 0 {
        eprintln!("DEBUG STREAMING: Empty object detected, setting JSON string representation");
        let mut vector = output.flat_vector(0);
        let cstring = std::ffi::CString::new("{}")?;
        vector.insert(row_idx, cstring);
    }

    Ok(())
}

// Unified recursive JSON value insertion system
// This is the core function that handles ALL JSON nesting patterns uniformly
fn insert_json_value_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            set_vector_null_unified(&vector_context, row_idx, expected_type);
            Ok(())
        }
        ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::Boolean(b))?;
                }
                _ => return Err("Invalid vector context for primitive boolean".into()),
            }
            Ok(())
        }
        ValueType::Number => {
            let number_str = json_reader.next_number_as_str()?;
            let number_value: f64 = number_str.parse().unwrap_or(0.0);
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::Number(number_value))?;
                }
                _ => return Err("Invalid vector context for primitive number".into()),
            }
            Ok(())
        }
        ValueType::String => {
            let s = json_reader.next_string()?;
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::String(s))?;
                }
                _ => return Err("Invalid vector context for primitive string".into()),
            }
            Ok(())
        }
        ValueType::Array => {
            insert_array_recursive(json_reader, vector_context, row_idx, expected_type)
        }
        ValueType::Object => {
            insert_object_recursive(json_reader, vector_context, row_idx, expected_type)
        }
    }
}

// Unified vector context that can represent any DuckDB vector type
enum VectorContext<'a> {
    DataChunk(&'a DataChunkHandle, usize), // (output, col_idx)
}

// Read a JSON value using streaming parser and write directly to DuckDB vector
// This is now a wrapper around the unified recursive system
fn read_value_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    let vector_context = VectorContext::DataChunk(output, col_idx);
    insert_json_value_recursive(json_reader, vector_context, row_idx, expected_type)
}

// Unified null value insertion
fn set_vector_null_unified(
    vector_context: &VectorContext,
    row_idx: usize,
    value_type: &JsonValueType,
) {
    match vector_context {
        VectorContext::DataChunk(output, col_idx) => {
            set_vector_null(output, *col_idx, row_idx, value_type);
        }
    }
}

// Unified primitive value insertion - redesigned to avoid borrowing issues
fn insert_primitive_to_data_chunk(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    temp_value: &TempValue,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut vector = output.flat_vector(col_idx);
    insert_temp_value_to_vector(&mut vector, row_idx, temp_value, &JsonValueType::String)
}


// Unified array insertion
fn insert_array_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Array(_element_type) = expected_type {
        match vector_context {
            VectorContext::DataChunk(output, col_idx) => {
                read_array_streaming_to_vector(json_reader, output, col_idx, row_idx, expected_type)
            }
        }
    } else {
        Err("Expected array type but got different type".into())
    }
}

// Unified object insertion
fn insert_object_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Object(_fields) = expected_type {
        match vector_context {
            VectorContext::DataChunk(output, col_idx) => {
                read_object_streaming_to_struct_vector(json_reader, output, col_idx, row_idx, expected_type)
            }
        }
    } else {
        Err("Expected object type but got different type".into())
    }
}

// Read array from streaming parser and write directly to DuckDB list vector
fn read_array_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    if let JsonValueType::Array(element_type) = expected_type {
        let mut list_vector = output.list_vector(col_idx);
        json_reader.begin_array()?;

        match element_type.as_ref() {
            JsonValueType::Object(fields) => {
                // Array of objects - implement proper streaming solution
                eprintln!("DEBUG ARRAY: Processing array of objects with {} fields", fields.len());

                // Collect all array elements first (we need to know the count for DuckDB)
                let mut object_elements = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Object => {
                            // Read the object into a temporary structure
                            let obj_data = read_object_to_temp_structure(json_reader, fields)?;
                            object_elements.push(Some(obj_data));
                        }
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            object_elements.push(None);
                        }
                        _ => {
                            // Skip invalid elements
                            json_reader.skip_value()?;
                            object_elements.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // Set up the list entry
                list_vector.set_entry(row_idx, 0, object_elements.len());

                // Get the struct child vector for the array elements
                let mut struct_child_vector = list_vector.struct_child(object_elements.len());

                // Insert each object into the struct vector
                for (elem_idx, obj_data) in object_elements.iter().enumerate() {
                    if let Some(field_values) = obj_data {
                        // Insert each field of this object
                        for (field_idx, field) in fields.iter().enumerate() {
                            let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());

                            if let Some(field_value) = field_values.get(&field.name) {
                                insert_temp_value_to_vector(&mut field_vector, elem_idx, field_value, &field.value_type)?;
                            } else {
                                field_vector.set_null(elem_idx);
                            }
                        }
                    } else {
                        // Null object - set all fields as null
                        for field_idx in 0..fields.len() {
                            let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());
                            field_vector.set_null(elem_idx);
                        }
                    }
                }

                eprintln!("DEBUG ARRAY: Successfully processed {} object elements", object_elements.len());
            }
            JsonValueType::Array(nested_element_type) => {
                // Multi-dimensional array - array of arrays
                eprintln!("DEBUG ARRAY: Processing multi-dimensional array with nested element type: {:?}", nested_element_type);

                // Collect all nested arrays using the new recursive approach
                let mut nested_arrays = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Array => {
                            // Process nested array recursively
                            eprintln!("DEBUG ARRAY: Processing nested array element");

                            // Use the new recursive data collection
                            let nested_array_data = collect_nested_array_data_recursive(json_reader, nested_element_type)?;
                            nested_arrays.push(Some(nested_array_data));
                        }
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            nested_arrays.push(None);
                        }
                        _ => {
                            // Skip invalid elements
                            json_reader.skip_value()?;
                            nested_arrays.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // Set up the list entry
                list_vector.set_entry(row_idx, 0, nested_arrays.len());

                // Get the list child vector for nested arrays
                let mut nested_list_vector = list_vector.list_child();

                // Insert each nested array using the new recursive approach
                for (array_idx, nested_array_data) in nested_arrays.iter().enumerate() {
                    if let Some(array_data) = nested_array_data {
                        // Direct recursive insertion - no conversion needed
                        insert_nested_array_recursive(&mut nested_list_vector, array_idx, array_data, nested_element_type)?;
                    } else {
                        // Null nested array
                        nested_list_vector.set_null(array_idx);
                    }
                }

                eprintln!("DEBUG ARRAY: Successfully processed {} nested arrays", nested_arrays.len());
            }
            _ => {
                // Array of primitives - collect and process
                let mut elements = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            elements.push(None);
                        }
                        struson::reader::ValueType::Boolean => {
                            let b = json_reader.next_bool()?;
                            elements.push(Some(b.to_string()));
                        }
                        struson::reader::ValueType::Number => {
                            let number_str = json_reader.next_number_as_str()?;
                            elements.push(Some(number_str.to_string()));
                        }
                        struson::reader::ValueType::String => {
                            let s = json_reader.next_string()?;
                            elements.push(Some(s));
                        }
                        _ => {
                            // Skip complex types in primitive arrays
                            json_reader.skip_value()?;
                            elements.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // Set up the list entry
                list_vector.set_entry(row_idx, 0, elements.len());

                // Insert primitive elements based on type
                match element_type.as_ref() {
                    JsonValueType::String => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let cstring = std::ffi::CString::new(s.as_str())?;
                                child_vector.insert(i, cstring);
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    JsonValueType::Number => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let n: f64 = s.parse().unwrap_or(0.0);
                                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(elements.len());
                                data_slice[i] = n;
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    JsonValueType::Boolean => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let b: bool = s.parse().unwrap_or(false);
                                let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(elements.len());
                                data_slice[i] = b;
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    _ => {
                        // For other primitive types, set all as null
                        let mut child_vector = list_vector.child(elements.len());
                        for i in 0..elements.len() {
                            child_vector.set_null(i);
                        }
                    }
                }
            }
        }

        Ok(())
    } else {
        Err("Expected array type but got different type".into())
    }
}

// Temporary value type for collecting object data during streaming
#[derive(Debug, Clone)]
enum TempValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
}

// Read object from streaming parser into temporary structure
fn read_object_to_temp_structure(
    json_reader: &mut JsonStreamReader<File>,
    fields: &[JsonField],
) -> Result<std::collections::HashMap<String, TempValue>, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    let mut field_values = std::collections::HashMap::new();

    json_reader.begin_object()?;

    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();

        // Only collect fields that are in our schema
        if fields.iter().any(|f| f.name == field_name) {
            let temp_value = match json_reader.peek()? {
                struson::reader::ValueType::Null => {
                    json_reader.next_null()?;
                    TempValue::Null
                }
                struson::reader::ValueType::Boolean => {
                    let b = json_reader.next_bool()?;
                    TempValue::Boolean(b)
                }
                struson::reader::ValueType::Number => {
                    let number_str = json_reader.next_number_as_str()?;
                    let number_value: f64 = number_str.parse().unwrap_or(0.0);
                    TempValue::Number(number_value)
                }
                struson::reader::ValueType::String => {
                    let s = json_reader.next_string()?;
                    TempValue::String(s)
                }
                _ => {
                    // For complex types (objects, arrays), skip for now
                    json_reader.skip_value()?;
                    TempValue::Null
                }
            };

            field_values.insert(field_name, temp_value);
        } else {
            // Skip fields not in schema
            json_reader.skip_value()?;
        }
    }

    json_reader.end_object()?;

    Ok(field_values)
}

// Insert temporary value into DuckDB vector
fn insert_temp_value_to_vector(
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    temp_value: &TempValue,
    _expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    match temp_value {
        TempValue::Null => {
            vector.set_null(row_idx);
        }
        TempValue::Boolean(b) => {
            let data_slice: &mut [bool] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = *b;
        }
        TempValue::Number(n) => {
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = *n;
        }
        TempValue::String(s) => {
            let cstring = std::ffi::CString::new(s.as_str())?;
            vector.insert(row_idx, cstring);
        }
    }

    Ok(())
}

// Read object from streaming parser and write directly to DuckDB struct vector
fn read_object_streaming_to_struct_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Object(fields) = expected_type {
        let mut struct_vector = output.struct_vector(col_idx);
        read_object_streaming_to_struct_recursive(json_reader, &mut struct_vector, fields, row_idx)
    } else {
        Err("Expected object type but got different type".into())
    }
}

// Recursive helper to read object fields directly into struct vector using streaming
fn read_object_streaming_to_struct_recursive(
    json_reader: &mut JsonStreamReader<File>,
    struct_vector: &mut duckdb::core::StructVector,
    fields: &[JsonField],
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG STREAMING: Reading object to struct with {} fields at row {}", fields.len(), row_idx);

    json_reader.begin_object()?;

    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();
        eprintln!("DEBUG STREAMING: Processing struct field: {}", field_name);

        // Find the field in our schema
        if let Some(field_idx) = fields.iter().position(|f| f.name == field_name) {
            let field = &fields[field_idx];
            eprintln!("DEBUG STREAMING: Found field {} at index {}", field_name, field_idx);

            match &field.value_type {
                JsonValueType::Object(nested_fields) => {
                    // Recursive struct handling
                    let mut nested_struct_vector = struct_vector.struct_vector_child(field_idx);
                    if let Err(e) = read_object_streaming_to_struct_recursive(json_reader, &mut nested_struct_vector, nested_fields, 0) {
                        eprintln!("DEBUG STREAMING: Error in nested struct {}: {}", field_name, e);
                        nested_struct_vector.set_null(0);
                    }
                }
                JsonValueType::Array(element_type) => {
                    // Array field handling within STRUCT - implement complete solution
                    eprintln!("DEBUG STREAMING: Processing array field {} with element type: {:?}", field_name, element_type);
                    let mut list_vector = struct_vector.list_vector_child(field_idx);

                    match insert_array_within_struct(json_reader, &mut list_vector, element_type) {
                        Ok(()) => {
                            eprintln!("DEBUG STREAMING: Successfully processed array field {}", field_name);
                        }
                        Err(e) => {
                            eprintln!("DEBUG STREAMING: Error processing array field {}: {}", field_name, e);
                            list_vector.set_null(0);
                        }
                    }
                }
                _ => {
                    // Primitive field - read directly into child vector
                    let mut field_vector = struct_vector.child(field_idx, 1);
                    match read_primitive_streaming_to_vector(json_reader, &mut field_vector, 0, &field.value_type) {
                        Ok(()) => {
                            eprintln!("DEBUG STREAMING: Successfully inserted primitive field {}", field_name);
                        }
                        Err(e) => {
                            eprintln!("DEBUG STREAMING: Error inserting primitive field {}: {}", field_name, e);
                            field_vector.set_null(0);
                        }
                    }
                }
            }
        } else {
            // Field not in schema - skip it
            eprintln!("DEBUG STREAMING: Skipping unknown field: {}", field_name);
            json_reader.skip_value()?;
        }
    }

    json_reader.end_object()?;
    Ok(())
}

// Enhanced temporary value type that can handle nested arrays
#[derive(Debug, Clone)]
enum NestedTempValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
    Array(Vec<NestedTempValue>), // Recursive array support
}

// Collect nested array data for multi-dimensional arrays with true recursion
fn collect_nested_array_data_recursive(
    json_reader: &mut JsonStreamReader<File>,
    element_type: &JsonValueType,
) -> Result<Vec<NestedTempValue>, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    let mut elements = Vec::new();
    json_reader.begin_array()?;

    while json_reader.has_next()? {
        match json_reader.peek()? {
            struson::reader::ValueType::Null => {
                json_reader.next_null()?;
                elements.push(NestedTempValue::Null);
            }
            struson::reader::ValueType::Boolean => {
                let b = json_reader.next_bool()?;
                elements.push(NestedTempValue::Boolean(b));
            }
            struson::reader::ValueType::Number => {
                let number_str = json_reader.next_number_as_str()?;
                let number_value: f64 = number_str.parse().unwrap_or(0.0);
                elements.push(NestedTempValue::Number(number_value));
            }
            struson::reader::ValueType::String => {
                let s = json_reader.next_string()?;
                elements.push(NestedTempValue::String(s));
            }
            struson::reader::ValueType::Array => {
                // Recursive array processing - handle arbitrary depth
                eprintln!("DEBUG NESTED: Processing deeper nested array");
                if let JsonValueType::Array(deeper_element_type) = element_type {
                    let nested_array = collect_nested_array_data_recursive(json_reader, deeper_element_type)?;
                    elements.push(NestedTempValue::Array(nested_array));
                } else {
                    // Type mismatch - skip
                    json_reader.skip_value()?;
                    elements.push(NestedTempValue::Null);
                }
            }
            _ => {
                // Skip other complex types
                json_reader.skip_value()?;
                elements.push(NestedTempValue::Null);
            }
        }
    }

    json_reader.end_array()?;
    Ok(elements)
}

// Legacy function for backward compatibility - convert to old format
fn collect_nested_array_data(
    json_reader: &mut JsonStreamReader<File>,
    element_type: &JsonValueType,
) -> Result<Vec<TempValue>, Box<dyn std::error::Error>> {
    let nested_data = collect_nested_array_data_recursive(json_reader, element_type)?;

    // Convert NestedTempValue to TempValue (flattening for now)
    let mut flat_data = Vec::new();
    for nested_value in nested_data {
        match nested_value {
            NestedTempValue::Null => flat_data.push(TempValue::Null),
            NestedTempValue::Boolean(b) => flat_data.push(TempValue::Boolean(b)),
            NestedTempValue::Number(n) => flat_data.push(TempValue::Number(n)),
            NestedTempValue::String(s) => flat_data.push(TempValue::String(s)),
            NestedTempValue::Array(_) => {
                // For now, mark nested arrays as null in the flat representation
                // This needs proper recursive vector insertion
                flat_data.push(TempValue::Null);
            }
        }
    }

    Ok(flat_data)
}

// Convert TempValue array to NestedTempValue for recursive processing
fn collect_nested_array_data_recursive_from_temp(
    temp_data: &[TempValue],
    _element_type: &JsonValueType,
) -> Result<Vec<NestedTempValue>, Box<dyn std::error::Error>> {
    let mut nested_data = Vec::new();
    for temp_value in temp_data {
        let nested_value = match temp_value {
            TempValue::Null => NestedTempValue::Null,
            TempValue::Boolean(b) => NestedTempValue::Boolean(*b),
            TempValue::Number(n) => NestedTempValue::Number(*n),
            TempValue::String(s) => NestedTempValue::String(s.clone()),
        };
        nested_data.push(nested_value);
    }
    Ok(nested_data)
}

// Insert nested array data recursively into list vector
fn insert_nested_array_recursive(
    list_vector: &mut duckdb::core::ListVector,
    array_idx: usize,
    nested_data: &[NestedTempValue],
    element_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    // Set up the list entry for this array
    list_vector.set_entry(array_idx, 0, nested_data.len());

    match element_type {
        JsonValueType::Array(deeper_element_type) => {
            // Multi-dimensional: each element is another array
            let mut deeper_list_vector = list_vector.list_child();

            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::Array(deeper_array) => {
                        // Recursively insert the deeper array
                        insert_nested_array_recursive(&mut deeper_list_vector, elem_idx, deeper_array, deeper_element_type)?;
                    }
                    NestedTempValue::Null => {
                        deeper_list_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        deeper_list_vector.set_null(elem_idx);
                    }
                }
            }
        }
        JsonValueType::Number => {
            // Array of numbers - insert directly
            let mut child_vector = list_vector.child(nested_data.len());
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::Number(n) => {
                        let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(nested_data.len());
                        data_slice[elem_idx] = *n;
                    }
                    NestedTempValue::Null => {
                        child_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(elem_idx);
                    }
                }
            }
        }
        JsonValueType::String => {
            // Array of strings - insert directly
            let mut child_vector = list_vector.child(nested_data.len());
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::String(s) => {
                        let cstring = std::ffi::CString::new(s.as_str())?;
                        child_vector.insert(elem_idx, cstring);
                    }
                    NestedTempValue::Null => {
                        child_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(elem_idx);
                    }
                }
            }
        }
        JsonValueType::Boolean => {
            // Array of booleans - insert directly
            let mut child_vector = list_vector.child(nested_data.len());
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::Boolean(b) => {
                        let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(nested_data.len());
                        data_slice[elem_idx] = *b;
                    }
                    NestedTempValue::Null => {
                        child_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(elem_idx);
                    }
                }
            }
        }
        _ => {
            // For other types, set all as null
            let mut child_vector = list_vector.child(nested_data.len());
            for elem_idx in 0..nested_data.len() {
                child_vector.set_null(elem_idx);
            }
        }
    }

    Ok(())
}

// Insert nested array elements into list vector
fn insert_nested_array_elements(
    nested_list_vector: &mut duckdb::core::ListVector,
    array_idx: usize,
    array_data: &[TempValue],
    element_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    match element_type {
        JsonValueType::Number => {
            let mut child_vector = nested_list_vector.child(array_data.len());
            for (i, temp_value) in array_data.iter().enumerate() {
                match temp_value {
                    TempValue::Number(n) => {
                        let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(array_data.len());
                        data_slice[i] = *n;
                    }
                    TempValue::Null => {
                        child_vector.set_null(i);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(i);
                    }
                }
            }
        }
        JsonValueType::String => {
            let mut child_vector = nested_list_vector.child(array_data.len());
            for (i, temp_value) in array_data.iter().enumerate() {
                match temp_value {
                    TempValue::String(s) => {
                        let cstring = std::ffi::CString::new(s.as_str())?;
                        child_vector.insert(i, cstring);
                    }
                    TempValue::Null => {
                        child_vector.set_null(i);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(i);
                    }
                }
            }
        }
        JsonValueType::Boolean => {
            let mut child_vector = nested_list_vector.child(array_data.len());
            for (i, temp_value) in array_data.iter().enumerate() {
                match temp_value {
                    TempValue::Boolean(b) => {
                        let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(array_data.len());
                        data_slice[i] = *b;
                    }
                    TempValue::Null => {
                        child_vector.set_null(i);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(i);
                    }
                }
            }
        }
        _ => {
            // For other types, set all as null for now
            let mut child_vector = nested_list_vector.child(array_data.len());
            for i in 0..array_data.len() {
                child_vector.set_null(i);
            }
        }
    }

    Ok(())
}

// Insert array within struct context - handles arrays as fields of objects
fn insert_array_within_struct(
    json_reader: &mut JsonStreamReader<File>,
    list_vector: &mut duckdb::core::ListVector,
    element_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG ARRAY_IN_STRUCT: Processing array with element type: {:?}", element_type);

    json_reader.begin_array()?;

    match element_type {
        JsonValueType::Object(fields) => {
            // Array of objects within struct - collect and process
            let mut object_elements = Vec::new();
            while json_reader.has_next()? {
                match json_reader.peek()? {
                    struson::reader::ValueType::Object => {
                        let obj_data = read_object_to_temp_structure(json_reader, fields)?;
                        object_elements.push(Some(obj_data));
                    }
                    struson::reader::ValueType::Null => {
                        json_reader.next_null()?;
                        object_elements.push(None);
                    }
                    _ => {
                        json_reader.skip_value()?;
                        object_elements.push(None);
                    }
                }
            }

            json_reader.end_array()?;

            // Set up the list entry for row 0 (since this is within a struct)
            list_vector.set_entry(0, 0, object_elements.len());

            // Get the struct child vector for the array elements
            let struct_child_vector = list_vector.struct_child(object_elements.len());

            // Insert each object into the struct vector
            for (elem_idx, obj_data) in object_elements.iter().enumerate() {
                if let Some(field_values) = obj_data {
                    // Insert each field of this object
                    for (field_idx, field) in fields.iter().enumerate() {
                        let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());

                        if let Some(field_value) = field_values.get(&field.name) {
                            insert_temp_value_to_vector(&mut field_vector, elem_idx, field_value, &field.value_type)?;
                        } else {
                            field_vector.set_null(elem_idx);
                        }
                    }
                } else {
                    // Null object - set all fields as null
                    for field_idx in 0..fields.len() {
                        let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());
                        field_vector.set_null(elem_idx);
                    }
                }
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Successfully processed {} object elements", object_elements.len());
        }
        JsonValueType::Array(_nested_element_type) => {
            // Multi-dimensional array - array of arrays
            eprintln!("DEBUG ARRAY_IN_STRUCT: Multi-dimensional array detected");

            // For now, implement basic support - count elements and set up structure
            let mut array_count = 0;
            while json_reader.has_next()? {
                // For multi-dimensional arrays, we need more complex handling
                // For now, skip the nested arrays and count them
                json_reader.skip_value()?;
                array_count += 1;
            }

            json_reader.end_array()?;

            // Set up the list entry
            list_vector.set_entry(0, 0, array_count);

            // For now, set all nested arrays as null - this needs full implementation
            let mut nested_list_vector = list_vector.list_child();
            for i in 0..array_count {
                nested_list_vector.set_null(i);
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Multi-dimensional array with {} elements (set as null for now)", array_count);
        }
        _ => {
            // Array of primitives within struct
            let mut elements = Vec::new();
            while json_reader.has_next()? {
                match json_reader.peek()? {
                    struson::reader::ValueType::Null => {
                        json_reader.next_null()?;
                        elements.push(None);
                    }
                    struson::reader::ValueType::Boolean => {
                        let b = json_reader.next_bool()?;
                        elements.push(Some(b.to_string()));
                    }
                    struson::reader::ValueType::Number => {
                        let number_str = json_reader.next_number_as_str()?;
                        elements.push(Some(number_str.to_string()));
                    }
                    struson::reader::ValueType::String => {
                        let s = json_reader.next_string()?;
                        elements.push(Some(s));
                    }
                    _ => {
                        json_reader.skip_value()?;
                        elements.push(None);
                    }
                }
            }

            json_reader.end_array()?;

            // Set up the list entry
            list_vector.set_entry(0, 0, elements.len());

            // Insert primitive elements
            match element_type {
                JsonValueType::String => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let cstring = std::ffi::CString::new(s.as_str())?;
                            child_vector.insert(i, cstring);
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                JsonValueType::Number => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let n: f64 = s.parse().unwrap_or(0.0);
                            let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(elements.len());
                            data_slice[i] = n;
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                JsonValueType::Boolean => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let b: bool = s.parse().unwrap_or(false);
                            let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(elements.len());
                            data_slice[i] = b;
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                _ => {
                    // For other types, set all as null
                    let mut child_vector = list_vector.child(elements.len());
                    for i in 0..elements.len() {
                        child_vector.set_null(i);
                    }
                }
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Successfully processed {} primitive elements", elements.len());
        }
    }

    Ok(())
}

// Read primitive value from streaming parser and write directly to vector
fn read_primitive_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    value_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            vector.set_null(row_idx);
            Ok(())
        }
        ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            let data_slice: &mut [bool] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = b;
            Ok(())
        }
        ValueType::Number => {
            let number_str = json_reader.next_number_as_str()?;
            let number_value: f64 = number_str.parse().unwrap_or(0.0);
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = number_value;
            Ok(())
        }
        ValueType::String => {
            let s = json_reader.next_string()?;
            let cstring = std::ffi::CString::new(s)?;
            vector.insert(row_idx, cstring);
            Ok(())
        }
        _ => {
            // For non-primitive types, set as null
            json_reader.skip_value()?;
            vector.set_null(row_idx);
            Ok(())
        }
    }
}

// Helper function to set null values in vectors based on type
fn set_vector_null(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    value_type: &JsonValueType,
) {
    match value_type {
        JsonValueType::Object(_) => {
            let mut struct_vector = output.struct_vector(col_idx);
            struct_vector.set_null(row_idx);
        }
        JsonValueType::Array(_) => {
            let mut list_vector = output.list_vector(col_idx);
            list_vector.set_null(row_idx);
        }
        _ => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_idx);
        }
    }
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data

// OPTION E: Recursive STRUCT insertion helper for arbitrary depth
fn insert_struct_recursive(
    struct_vector: &mut duckdb::core::StructVector,
    value: &serde_json::Value,
    fields: &[JsonField],
    depth: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG RECURSIVE: Inserting STRUCT with {} fields at depth {}", fields.len(), depth);

    if let serde_json::Value::Object(obj) = value {
        // Insert each field into the corresponding child vector
        for (field_idx, field) in fields.iter().enumerate() {
            let field_value = obj.get(&field.name).unwrap_or(&serde_json::Value::Null);
            eprintln!("DEBUG RECURSIVE: Processing field {} ({}) at depth {}", field_idx, field.name, depth);

            match &field.value_type {
                JsonValueType::Object(nested_fields) => {
                    // Recursive STRUCT handling
                    eprintln!("DEBUG RECURSIVE: Nested STRUCT field {} with {} fields", field.name, nested_fields.len());
                    let mut nested_struct_vector = struct_vector.struct_vector_child(field_idx);

                    // Recursive call for deeper nesting
                    if let Err(e) = insert_struct_recursive(&mut nested_struct_vector, field_value, nested_fields, depth + 1) {
                        eprintln!("DEBUG RECURSIVE: Error in nested STRUCT {}: {}", field.name, e);
                        nested_struct_vector.set_null(0);
                    }
                }
                JsonValueType::Array(element_type) => {
                    // Array field handling within STRUCT
                    eprintln!("DEBUG RECURSIVE: Array field {} with element type: {:?}", field.name, element_type);

                    // Get the list vector for this array field
                    let mut list_vector = struct_vector.list_vector_child(field_idx);

                    if let serde_json::Value::Array(arr) = field_value {
                        eprintln!("DEBUG RECURSIVE: Processing array with {} elements", arr.len());

                        // Set up the list entry
                        list_vector.set_entry(0, 0, arr.len());

                        // Insert each array element
                        for (elem_idx, elem) in arr.iter().enumerate() {
                            match element_type.as_ref() {
                                JsonValueType::Object(elem_fields) => {
                                    // Array of STRUCTs - use recursive STRUCT handling
                                    eprintln!("DEBUG RECURSIVE: Array element {} is STRUCT with {} fields", elem_idx, elem_fields.len());
                                    let mut elem_struct_vector = list_vector.struct_child(arr.len());

                                    if let Err(e) = insert_struct_recursive(&mut elem_struct_vector, elem, elem_fields, depth + 1) {
                                        eprintln!("DEBUG RECURSIVE: Error inserting STRUCT array element {}: {}", elem_idx, e);
                                        elem_struct_vector.set_null(elem_idx);
                                    }
                                }
                                _ => {
                                    // Array of primitives
                                    let mut elem_vector = list_vector.child(arr.len());
                                    if let Err(e) = insert_primitive_value(&mut elem_vector, elem_idx, elem, element_type) {
                                        eprintln!("DEBUG RECURSIVE: Error inserting primitive array element {}: {}", elem_idx, e);
                                        elem_vector.set_null(elem_idx);
                                    }
                                }
                            }
                        }
                    } else if field_value.is_null() {
                        eprintln!("DEBUG RECURSIVE: Array field is null, setting as null");
                        list_vector.set_null(0);
                    } else {
                        eprintln!("DEBUG RECURSIVE: Array field is not an array, setting as null");
                        list_vector.set_null(0);
                    }
                }
                _ => {
                    // Primitive field handling
                    let mut field_vector = struct_vector.child(field_idx, 1);
                    if let Err(e) = insert_primitive_value_with_depth(&mut field_vector, 0, field_value, &field.value_type, depth) {
                        eprintln!("DEBUG RECURSIVE: Error inserting primitive field {}: {}", field.name, e);
                        field_vector.set_null(0);
                    }
                }
            }
        }
    } else if value.is_null() {
        eprintln!("DEBUG RECURSIVE: STRUCT value is null, setting as null");
        struct_vector.set_null(0);
    } else {
        eprintln!("DEBUG RECURSIVE: STRUCT value is not an object, setting as null");
        struct_vector.set_null(0);
    }

    Ok(())
}

// OPTION E: Insert structured values using pure recursive STRUCT handling
fn insert_structured_value(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    value: &serde_json::Value,
    column_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG INSERT: Inserting value type {:?} into column {}", column_type, col_idx);

    match column_type {
        JsonValueType::Object(fields) => {
            // Handle STRUCT type using recursive helper
            eprintln!("DEBUG INSERT: Handling STRUCT with {} fields", fields.len());
            let mut struct_vector = output.struct_vector(col_idx);
            insert_struct_recursive(&mut struct_vector, value, fields, 0)
        }
        JsonValueType::Array(element_type) => {
            // Handle LIST type with proper structured insertion
            eprintln!("DEBUG INSERT: Handling ARRAY with element type: {:?}", element_type);
            let mut list_vector = output.list_vector(col_idx);

            if let serde_json::Value::Array(arr) = value {
                eprintln!("DEBUG INSERT: Processing array with {} elements", arr.len());

                // Set up the list entry - this tells DuckDB how many elements are in this list
                list_vector.set_entry(row_idx, 0, arr.len());

                // Insert each array element
                for (elem_idx, elem) in arr.iter().enumerate() {
                    match element_type.as_ref() {
                        JsonValueType::Object(fields) => {
                            // For STRUCT elements, use struct_child() to get a StructVector
                            eprintln!("DEBUG INSERT: Inserting STRUCT element {} with {} fields", elem_idx, fields.len());

                            let mut struct_vector = list_vector.struct_child(arr.len());

                            if let serde_json::Value::Object(obj) = elem {
                                eprintln!("DEBUG INSERT: Processing STRUCT object with {} fields", obj.len());

                                // Insert each field into the corresponding child vector of the STRUCT
                                for (field_idx, field) in fields.iter().enumerate() {
                                    let field_value = obj.get(&field.name).unwrap_or(&serde_json::Value::Null);
                                    eprintln!("DEBUG INSERT: Processing STRUCT field {} ({}) in array element {}", field_idx, field.name, elem_idx);

                                    // Get the child vector for this field within the STRUCT
                                    let mut field_vector = struct_vector.child(field_idx, arr.len());

                                    // Insert the field value using primitive insertion
                                    if let Err(e) = insert_primitive_value(&mut field_vector, elem_idx, field_value, &field.value_type) {
                                        eprintln!("DEBUG INSERT: Error inserting STRUCT field {}: {}", field.name, e);
                                        field_vector.set_null(elem_idx);
                                    }
                                }
                            } else if elem.is_null() {
                                eprintln!("DEBUG INSERT: STRUCT element is null, setting as null");
                                struct_vector.set_null(elem_idx);
                            } else {
                                eprintln!("DEBUG INSERT: STRUCT element is not an object, setting as null");
                                struct_vector.set_null(elem_idx);
                            }
                        }
                        JsonValueType::Array(_) => {
                            // For nested arrays, use child() for now and convert to JSON string
                            // TODO: Implement proper nested ARRAY handling
                            eprintln!("DEBUG INSERT: Converting nested ARRAY to JSON string");
                            let child_vector = list_vector.child(arr.len());
                            let json_str = serde_json::to_string(elem)?;
                            let cstring = std::ffi::CString::new(json_str)?;
                            child_vector.insert(elem_idx, cstring);
                        }
                        _ => {
                            // For primitive types, use the regular child() method
                            let mut child_vector = list_vector.child(arr.len());
                            if let Err(e) = insert_primitive_value(&mut child_vector, elem_idx, elem, element_type) {
                                eprintln!("DEBUG INSERT: Error inserting array element {}: {}", elem_idx, e);
                                child_vector.set_null(elem_idx);
                            }
                        }
                    }
                }
            } else if value.is_null() {
                eprintln!("DEBUG INSERT: Array value is null, setting as null");
                list_vector.set_null(row_idx);
            } else {
                eprintln!("DEBUG INSERT: Array value is not an array, setting as null");
                list_vector.set_null(row_idx);
            }
            Ok(())
        }
        _ => {
            // For primitive types, use flat vector insertion
            let mut vector = output.flat_vector(col_idx);
            insert_primitive_value(&mut vector, row_idx, value, column_type)
        }
    }
}

// Insert a value into a vector (can be primitive or structured) with depth protection
fn insert_primitive_value(
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    value: &serde_json::Value,
    value_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    insert_primitive_value_with_depth(vector, row_idx, value, value_type, 0)
}

// OPTION E: Insert primitive values with proper type handling (no VARCHAR fallbacks)
fn insert_primitive_value_with_depth(
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    value: &serde_json::Value,
    value_type: &JsonValueType,
    depth: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    // OPTION E: No artificial depth limits in primitive insertion

    eprintln!("DEBUG PRIMITIVE: Inserting value type {:?} at row {} (depth: {})", value_type, row_idx, depth);

    match value_type {
        JsonValueType::String => {
            let s = value.as_str().unwrap_or("null");
            let cstring = match std::ffi::CString::new(s) {
                Ok(s) => s,
                Err(_) => match std::ffi::CString::new("null") {
                    Ok(s) => s,
                    Err(_) => unsafe { std::ffi::CString::from_vec_unchecked(vec![b'n', b'u', b'l', b'l', 0]) }
                }
            };
            vector.insert(row_idx, cstring);
        }
        JsonValueType::Number => {
            let n = value.as_f64().unwrap_or(0.0);
            eprintln!("DEBUG PRIMITIVE: Inserting number value: {}", n);

            // The DuckDB Rust API FlatVector doesn't implement Inserter<f64>
            // We need to use direct memory access via as_mut_slice
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = n;
            eprintln!("DEBUG PRIMITIVE: Successfully inserted f64 value {} at index {}", n, row_idx);
        }
        JsonValueType::Boolean => {
            let b = value.as_bool().unwrap_or(false);
            eprintln!("DEBUG PRIMITIVE: Inserting boolean value: {}", b);

            // The DuckDB Rust API FlatVector doesn't implement Inserter<bool>
            // We need to use direct memory access via as_mut_slice
            let data_slice: &mut [bool] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = b;
            eprintln!("DEBUG PRIMITIVE: Successfully inserted bool value {} at index {}", b, row_idx);
        }
        JsonValueType::Null => {
            // Set the vector element as NULL instead of inserting the string "null"
            vector.set_null(row_idx);
            eprintln!("DEBUG PRIMITIVE: Set NULL value at index {}", row_idx);
        }
        JsonValueType::Object(_) => {
            // CRITICAL: NEVER convert STRUCT data to VARCHAR in primitive context
            // This indicates a design error - STRUCT types should not reach primitive insertion
            eprintln!("ERROR: STRUCT type reached primitive insertion context - this should not happen");
            eprintln!("ERROR: Setting as NULL to prevent VARCHAR fallback violation");
            vector.set_null(row_idx);
        }
        JsonValueType::Array(_) => {
            // CRITICAL: NEVER convert ARRAY data to VARCHAR in primitive context
            // This indicates a design error - ARRAY types should not reach primitive insertion
            eprintln!("ERROR: ARRAY type reached primitive insertion context - this should not happen");
            eprintln!("ERROR: Setting as NULL to prevent VARCHAR fallback violation");
            vector.set_null(row_idx);
        }
    }
    Ok(())
}

// Helper function to read and flatten JSON arrays generically
fn read_and_flatten_json(
    file_path: &str,
    schema: &JsonSchema,
    init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // For the first call, read the entire array and return first batch
    let current_element = init_data.current_element.load(Ordering::Relaxed);

    if current_element > 0 {
        // We've already processed some elements, return empty to indicate we're done
        // TODO: Implement proper streaming across multiple calls
        return Ok(vec![]);
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    let columns = &schema.columns;
    let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];
    let mut elements_read = 0;
    let max_elements = 100; // Limit to prevent memory issues

    match json_reader.peek()? {
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;

            // Look for the first array field
            while json_reader.has_next()? {
                let _field_name = json_reader.next_name()?;

                if let struson::reader::ValueType::Array = json_reader.peek()? {
                    json_reader.begin_array()?;

                    // Process array elements
                    while json_reader.has_next()? && elements_read < max_elements {
                        if let struson::reader::ValueType::Object = json_reader.peek()? {
                            json_reader.begin_object()?;
                            let mut row_data = vec!["".to_string(); columns.len()];

                            while json_reader.has_next()? {
                                let field_name = json_reader.next_name()?;
                                if let Some(col_idx) = columns.iter().position(|c| c.name == field_name) {
                                    // Extract the value for this column
                                    let value = match json_reader.peek()? {
                                        struson::reader::ValueType::String => json_reader.next_string()?,
                                        struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                        struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                        struson::reader::ValueType::Null => {
                                            json_reader.next_null()?;
                                            "null".to_string()
                                        }
                                        _ => {
                                            json_reader.skip_value()?;
                                            "".to_string()
                                        }
                                    };
                                    row_data[col_idx] = value;
                                } else {
                                    json_reader.skip_value()?;
                                }
                            }
                            json_reader.end_object()?;

                            // Add row data to result columns
                            for (col_idx, value) in row_data.into_iter().enumerate() {
                                result_columns[col_idx].push(value);
                            }
                            elements_read += 1;
                        } else {
                            json_reader.skip_value()?;
                            elements_read += 1;
                        }
                    }

                    break; // Found our array, stop looking
                } else {
                    json_reader.skip_value()?;
                }
            }
        }
        struson::reader::ValueType::Array => {
            // Direct array at root level
            json_reader.begin_array()?;

            while json_reader.has_next()? && elements_read < max_elements {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                    let mut row_data = vec!["".to_string(); columns.len()];

                    while json_reader.has_next()? {
                        let field_name = json_reader.next_name()?;
                        if let Some(col_idx) = columns.iter().position(|c| c.name == field_name) {
                            // Extract the value for this column
                            let value = match json_reader.peek()? {
                                struson::reader::ValueType::String => json_reader.next_string()?,
                                struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                struson::reader::ValueType::Null => {
                                    json_reader.next_null()?;
                                    "null".to_string()
                                }
                                _ => {
                                    json_reader.skip_value()?;
                                    "".to_string()
                                }
                            };
                            row_data[col_idx] = value;
                        } else {
                            json_reader.skip_value()?;
                        }
                    }
                    json_reader.end_object()?;

                    // Add row data to result columns
                    for (col_idx, value) in row_data.into_iter().enumerate() {
                        result_columns[col_idx].push(value);
                    }
                    elements_read += 1;
                } else {
                    json_reader.skip_value()?;
                    elements_read += 1;
                }
            }
        }
        _ => {
            return Err("Expected JSON object or array at root".into());
        }
    }

    // Mark as finished since we read everything in one go
    init_data.finished.store(true, Ordering::Relaxed);
    init_data.current_element.store(elements_read, Ordering::Relaxed);

    Ok(result_columns)
}





impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        eprintln!("DEBUG BIND: File path: {}", file_path);
        eprintln!("DEBUG BIND: Parameter count: {}", bind.get_parameter_count());

        // Try to explore what other information might be available in bind phase
        eprintln!("DEBUG BIND: Exploring BindInfo for additional projection information...");

        // Let's see if BindInfo has any methods that might give us nested field information
        // This is exploratory to understand what's available
        eprintln!("DEBUG BIND: Checking for nested field projection capabilities...");

        // Discover JSON schema from the file (no projection info available at bind time)
        let full_schema = match discover_json_schema(&file_path, None) {
            Ok(schema) => {
                eprintln!("DEBUG BIND: Discovered full schema: {:?}", schema);
                schema
            },
            Err(e) => {
                eprintln!("DEBUG BIND: Schema discovery failed: {}", e);
                return Err(e);
            }
        };

        // At bind time, we don't have projection info yet, so use full schema
        // Projection optimization will be applied during data reading
        eprintln!("DEBUG BIND: Using full schema at bind time (projection applied later)");

        // Add result columns to DuckDB based on full schema
        for (i, column) in full_schema.columns.iter().enumerate() {
            eprintln!("DEBUG BIND: Adding column '{}' at index {}", column.name, i);

            // Convert JSON types to DuckDB logical types (including STRUCT/ARRAY)
            let logical_type = json_type_to_duckdb_type(&column.value_type)?;

            bind.add_result_column(&column.name, logical_type);
            eprintln!("DEBUG BIND: Added '{}' as {:?} type", column.name, column.value_type);
        }

        Ok(JsonReaderBindData {
            file_path,
            schema: full_schema,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Test projection pushdown capabilities
        eprintln!("DEBUG: Testing projection pushdown capabilities");

        // Get column indices for projection pushdown
        let column_indices = init.get_column_indices();
        eprintln!("DEBUG: Projected column indices: {:?}", column_indices);
        eprintln!("DEBUG: Number of projected columns: {}", column_indices.len());

        // Get bind data to access column names
        let bind_data = unsafe { &*(init.get_bind_data() as *const JsonReaderBindData) };
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG: All available columns: {:?}", column_names);

        // Map indices to actual column names
        let projected_column_names: Vec<String> = column_indices
            .iter()
            .map(|&idx| {
                if (idx as usize) < column_names.len() {
                    column_names[idx as usize].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", idx)
                }
            })
            .collect();

        eprintln!("DEBUG: Projected column names: {:?}", projected_column_names);

        // Try to explore what other methods might be available on InitInfo
        eprintln!("DEBUG: Exploring InitInfo methods...");

        // Let's try to see if there are any other methods we can call on InitInfo
        // This is exploratory - we'll see what's available

        // Check if there's any way to get more detailed projection information
        eprintln!("DEBUG: Checking for additional projection information...");

        // Let's see if there are any other methods we can call
        // (This is exploratory - some might not exist)

        if column_indices.is_empty() {
            eprintln!("DEBUG: No specific columns projected (SELECT * query?)");
        } else {
            eprintln!("DEBUG: Specific columns requested: {:?}", projected_column_names);
        }

        // Convert column indices to simple usize vector
        let projected_columns: Vec<usize> = column_indices
            .iter()
            .map(|&idx| idx as usize)
            .collect();

        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = unsafe { &*(func.get_bind_data() as *const JsonReaderBindData) };

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        eprintln!("DEBUG FUNC: Processing file: {}", bind_data.file_path);
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG FUNC: Available columns: {:?}", column_names);

        // Apply projection optimization based on projected columns
        let projected_column_names: Vec<String> = init_data.projected_columns
            .iter()
            .map(|&col_idx| {
                if col_idx < column_names.len() {
                    column_names[col_idx].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", col_idx)
                }
            })
            .collect();

        eprintln!("DEBUG FUNC: Projected columns for optimization: {:?}", projected_column_names);

        // Apply projection pushdown to schema
        let optimized_schema = apply_projection_pushdown(&bind_data.schema, &projected_column_names);
        eprintln!("DEBUG FUNC: Using optimized schema: {:?}", optimized_schema);

        // Read JSON file using pure streaming and write directly to DuckDB vectors
        eprintln!("DEBUG FUNC: Reading JSON file with pure streaming to vectors");

        match read_json_streaming_to_vectors(&bind_data.file_path, &optimized_schema, output) {
            Ok(row_count) => {
                eprintln!("DEBUG FUNC: Successfully processed {} rows using pure streaming", row_count);

                if row_count == 0 {
                    output.set_len(0);
                } else {
                    output.set_len(row_count);
                    eprintln!("DEBUG FUNC: Set output length to {} rows", row_count);
                }

                // Mark as finished after processing the data
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished");
            }
            Err(e) => {
                eprintln!("ERROR: Failed to read JSON file '{}': {}", bind_data.file_path, e);

                // Provide helpful error messages based on error type
                let error_msg = if e.to_string().contains("not found") {
                    format!("JSON file not found: {}", bind_data.file_path)
                } else if e.to_string().contains("malformed") || e.to_string().contains("Failed to read") {
                    format!("Malformed JSON in file: {} - {}", bind_data.file_path, e)
                } else if e.to_string().contains("permission") || e.to_string().contains("Permission denied") {
                    format!("Permission denied reading file: {}", bind_data.file_path)
                } else if e.to_string().contains("too many fields") {
                    format!("JSON object too complex in file: {} - {}", bind_data.file_path, e)
                } else {
                    format!("Error reading JSON file '{}': {}", bind_data.file_path, e)
                };

                output.set_len(0);
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished due to error");
                return Err(error_msg.into());
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}